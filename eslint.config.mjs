import { defineConfig, globalIgnores } from "eslint/config";
import typescriptEslint from "@typescript-eslint/eslint-plugin";
import globals from "globals";
import tsParser from "@typescript-eslint/parser";
import path from "node:path";
import { fileURLToPath } from "node:url";
import js from "@eslint/js";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
    baseDirectory: __dirname,
    recommendedConfig: js.configs.recommended,
    allConfig: js.configs.all
});

export default defineConfig([globalIgnores([
    "dist/**/*",
    "build/**/*",
    "node_modules/**/*",
    "coverage/**/*",
    "**/*.d.ts",
    "scripts/*.js",
]), {
    extends: compat.extends("eslint:recommended", "plugin:@typescript-eslint/recommended", "prettier"),

    plugins: {
        "@typescript-eslint": typescriptEslint,
    },

    languageOptions: {
        globals: {
            ...globals.node,
        },

        parser: tsParser,
        ecmaVersion: 2022,
        sourceType: "module",
    },

    rules: {
        "@typescript-eslint/explicit-function-return-type": "off",
        "@typescript-eslint/explicit-module-boundary-types": "off",
        "@typescript-eslint/no-explicit-any": "warn",

        "@typescript-eslint/no-unused-vars": ["error", {
            argsIgnorePattern: "^_",
            varsIgnorePattern: "^_",
            destructuredArrayIgnorePattern: "^_",
        }],

        "@typescript-eslint/no-non-null-assertion": "warn",
        "@typescript-eslint/consistent-type-definitions": ["error", "interface"],

        "@typescript-eslint/array-type": ["error", {
            default: "array-simple",
        }],

        "no-console": ["warn", {
            allow: ["warn", "error", "info", "debug"],
        }],

        "prefer-const": "error",
        "no-var": "error",

        eqeqeq: ["error", "always", {
            null: "ignore",
        }],

        curly: ["error", "all"],
        "no-throw-literal": "error",
        "prefer-template": "error",
        "no-useless-concat": "error",
        "no-duplicate-imports": "error",
        "no-self-compare": "error",
        "no-unreachable-loop": "error",
        "no-use-before-define": "off",

        "@typescript-eslint/no-use-before-define": ["error", {
            functions: false,
        }],

        "no-process-exit": "warn",
        "handle-callback-err": "error",
        "no-path-concat": "error",

        "sort-imports": ["warn", {
            ignoreCase: false,
            ignoreDeclarationSort: true,
            ignoreMemberSort: false,
            memberSyntaxSortOrder: ["none", "all", "multiple", "single"],
            allowSeparatedGroups: true,
        }],

        "no-await-in-loop": "warn",
        "require-atomic-updates": "error",
        "prefer-promise-reject-errors": "error",
        "no-eval": "error",
        "no-implied-eval": "error",
        "no-new-func": "error",
        "no-script-url": "error",
    },
}, {
    files: ["scripts/*.js"],

    languageOptions: {
        globals: {
            ...globals.node,
        },

        ecmaVersion: 2022,
        sourceType: "module",
    },

    rules: {
        "@typescript-eslint/no-var-requires": "off",
        "@typescript-eslint/no-require-imports": "off",
    },
}, {
    files: ["src/tests/**/*", "src/**/*.test.ts", "src/**/*.spec.ts"],

    rules: {
        "@typescript-eslint/no-explicit-any": "off",
        "@typescript-eslint/no-non-null-assertion": "off",
        "no-console": "off",
    },
}, {
    files: ["src/types/**/*", "src/interfaces/**/*"],

    rules: {
        "@typescript-eslint/no-empty-interface": "off",
        "@typescript-eslint/consistent-type-definitions": "off",
    },
}]);