#!/usr/bin/env bun
import { Pool } from "pg";
import logger from "../src/utils/logger";

// Database connection configuration
const dbConfig = {
  host: process.env.DB_HOST || "localhost",
  port: parseInt(process.env.DB_PORT || "5432", 10),
  database: process.env.DB_NAME || "sanity_ai",
  user: process.env.DB_USER || "postgres",
  password: process.env.DB_PASSWORD || "password",
};

/**
 * Create stats tables in the database
 */
export const createStatsTables = async (): Promise<void> => {
  const pool = new Pool(dbConfig);
  
  try {
    logger.info("Creating stats tables...");

    // Create request_stats_daily table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS request_stats_daily (
        id SERIAL PRIMARY KEY,
        date DATE NOT NULL UNIQUE,
        total_requests INTEGER DEFAULT 0,
        blocked_requests INTEGER DEFAULT 0,
        allowed_requests INTEGER DEFAULT 0,
        text_requests INTEGER DEFAULT 0,
        image_requests INTEGER DEFAULT 0,
        batch_requests INTEGER DEFAULT 0,
        cache_hits INTEGER DEFAULT 0,
        cache_misses INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create api_performance_hourly table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS api_performance_hourly (
        id SERIAL PRIMARY KEY,
        date DATE NOT NULL,
        hour INTEGER NOT NULL,
        avg_response_time DECIMAL(10,2) DEFAULT 0,
        min_response_time DECIMAL(10,2) DEFAULT 0,
        max_response_time DECIMAL(10,2) DEFAULT 0,
        total_requests INTEGER DEFAULT 0,
        error_count INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(date, hour)
      );
    `);

    // Create content_flags_daily table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS content_flags_daily (
        id SERIAL PRIMARY KEY,
        date DATE NOT NULL UNIQUE,
        hate_speech INTEGER DEFAULT 0,
        harassment INTEGER DEFAULT 0,
        violence INTEGER DEFAULT 0,
        self_harm INTEGER DEFAULT 0,
        sexual INTEGER DEFAULT 0,
        shocking INTEGER DEFAULT 0,
        illegal INTEGER DEFAULT 0,
        spam INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create user_activity_daily table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS user_activity_daily (
        id SERIAL PRIMARY KEY,
        date DATE NOT NULL,
        user_id VARCHAR(255) NOT NULL,
        total_requests INTEGER DEFAULT 0,
        blocked_requests INTEGER DEFAULT 0,
        allowed_requests INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(date, user_id)
      );
    `);

    // Create indexes for better performance
    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_request_stats_daily_date ON request_stats_daily(date);
      CREATE INDEX IF NOT EXISTS idx_api_performance_hourly_date_hour ON api_performance_hourly(date, hour);
      CREATE INDEX IF NOT EXISTS idx_content_flags_daily_date ON content_flags_daily(date);
      CREATE INDEX IF NOT EXISTS idx_user_activity_daily_date_user ON user_activity_daily(date, user_id);
    `);

    logger.info("Stats tables created successfully");
  } catch (error) {
    logger.error("Error creating stats tables:", error);
    throw error;
  } finally {
    await pool.end();
  }
};

/**
 * Drop stats tables (for cleanup/reset)
 */
export const dropStatsTables = async (): Promise<void> => {
  const pool = new Pool(dbConfig);
  
  try {
    logger.info("Dropping stats tables...");

    await pool.query("DROP TABLE IF EXISTS user_activity_daily CASCADE;");
    await pool.query("DROP TABLE IF EXISTS content_flags_daily CASCADE;");
    await pool.query("DROP TABLE IF EXISTS api_performance_hourly CASCADE;");
    await pool.query("DROP TABLE IF EXISTS request_stats_daily CASCADE;");

    logger.info("Stats tables dropped successfully");
  } catch (error) {
    logger.error("Error dropping stats tables:", error);
    throw error;
  } finally {
    await pool.end();
  }
};

/**
 * Remove cache fields from existing tables (migration)
 */
export const removeCacheFields = async (): Promise<void> => {
  const pool = new Pool(dbConfig);
  
  try {
    logger.info("Removing cache fields from tables...");

    // Check if api_keys table exists and has cache fields
    const tableExists = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'api_keys'
      );
    `);

    if (tableExists.rows[0].exists) {
      // Check if cache fields exist
      const cacheFieldsExist = await pool.query(`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'api_keys' 
        AND column_name IN ('cache_data', 'cache_expiry');
      `);

      if (cacheFieldsExist.rows.length > 0) {
        logger.info("Removing cache fields from api_keys table...");
        
        // Remove cache_data column if it exists
        try {
          await pool.query("ALTER TABLE api_keys DROP COLUMN IF EXISTS cache_data;");
          logger.info("Removed cache_data column");
        } catch (error) {
          logger.warn("Could not remove cache_data column:", error);
        }

        // Remove cache_expiry column if it exists
        try {
          await pool.query("ALTER TABLE api_keys DROP COLUMN IF EXISTS cache_expiry;");
          logger.info("Removed cache_expiry column");
        } catch (error) {
          logger.warn("Could not remove cache_expiry column:", error);
        }
      } else {
        logger.info("No cache fields found in api_keys table");
      }
    } else {
      logger.info("api_keys table does not exist, skipping cache field removal");
    }

    logger.info("Cache field removal completed");
  } catch (error) {
    logger.error("Error removing cache fields:", error);
    throw error;
  } finally {
    await pool.end();
  }
};

/**
 * Main function to run database preparation
 */
const main = async (): Promise<void> => {
  try {
    logger.info("Starting database preparation...");
    
    // Create stats tables
    await createStatsTables();
    
    // Remove cache fields (migration)
    await removeCacheFields();
    
    logger.info("Database preparation completed successfully");
  } catch (error) {
    logger.error("Database preparation failed:", error);
    throw error;
  }
};

// Run main function if this file is executed directly
if (require.main === module) {
  main().catch((error) => {
    logger.error("Fatal error during database preparation:", error);
    throw error;
  });
}
