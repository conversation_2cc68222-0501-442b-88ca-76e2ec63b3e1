#!/usr/bin/env bun
import { cacheDelete, cacheGet, cacheSet, isRedisHealthy } from "../src/utils/redis";
import logger from "../src/utils/logger";

/**
 * Test Redis connection and basic operations
 */
const testRedis = async (): Promise<void> => {
  try {
    logger.info("Testing Redis connection...");
    logger.info("=".repeat(50));

    // Test 1: Health check
    logger.info("1. Testing Redis health check...");
    const isHealthy = await isRedisHealthy();
    if (isHealthy) {
      logger.info("✅ Redis is healthy");
    } else {
      logger.warn("⚠️ Redis health check failed - using memory cache fallback");
    }

    // Test 2: Basic set/get operations
    logger.info("\n2. Testing basic set/get operations...");
    const testKey = "test:redis:connection";
    const testValue = "Hello Redis!";

    await cacheSet(testKey, testValue);
    logger.info(`✅ Set key '${testKey}' with value '${testValue}'`);

    const retrievedValue = await cacheGet(testKey);
    if (retrievedValue === testValue) {
      logger.info(`✅ Retrieved correct value: '${retrievedValue}'`);
    } else {
      logger.error(`❌ Retrieved incorrect value: '${retrievedValue}', expected: '${testValue}'`);
    }

    // Test 3: TTL operations
    logger.info("\n3. Testing TTL operations...");
    const ttlKey = "test:redis:ttl";
    const ttlValue = "TTL Test";
    const ttlSeconds = 2;

    await cacheSet(ttlKey, ttlValue, ttlSeconds);
    logger.info(`✅ Set key '${ttlKey}' with TTL of ${ttlSeconds} seconds`);

    const immediateValue = await cacheGet(ttlKey);
    if (immediateValue === ttlValue) {
      logger.info(`✅ Immediate retrieval successful: '${immediateValue}'`);
    } else {
      logger.error(`❌ Immediate retrieval failed: '${immediateValue}'`);
    }

    // Wait for TTL to expire
    logger.info(`⏳ Waiting ${ttlSeconds + 1} seconds for TTL to expire...`);
    await new Promise(resolve => setTimeout(resolve, (ttlSeconds + 1) * 1000));

    const expiredValue = await cacheGet(ttlKey);
    if (expiredValue === null) {
      logger.info("✅ TTL expiration working correctly");
    } else {
      logger.warn(`⚠️ TTL might not be working: '${expiredValue}'`);
    }

    // Test 4: Delete operations
    logger.info("\n4. Testing delete operations...");
    const deleteKey = "test:redis:delete";
    const deleteValue = "Delete Test";

    await cacheSet(deleteKey, deleteValue);
    logger.info(`✅ Set key '${deleteKey}' for deletion test`);

    const beforeDelete = await cacheGet(deleteKey);
    if (beforeDelete === deleteValue) {
      logger.info("✅ Key exists before deletion");
    }

    const deleteResult = await cacheDelete(deleteKey);
    logger.info(`✅ Delete operation returned: ${deleteResult}`);

    const afterDelete = await cacheGet(deleteKey);
    if (afterDelete === null) {
      logger.info("✅ Key successfully deleted");
    } else {
      logger.error(`❌ Key still exists after deletion: '${afterDelete}'`);
    }

    // Cleanup
    logger.info("\n5. Cleaning up test keys...");
    await cacheDelete(testKey);
    await cacheDelete(ttlKey);
    await cacheDelete(deleteKey);
    logger.info("✅ Cleanup completed");

    logger.info(`\n${"=".repeat(50)}`);
    logger.info("🎉 Redis test completed successfully!");
    
  } catch (error) {
    logger.error("❌ Redis test failed:", error);
    throw error;
  }
};

/**
 * Main function
 */
const main = async (): Promise<void> => {
  try {
    await testRedis();
  } catch (error) {
    logger.error("Test execution failed:", error);
    throw error;
  }
};

// Run main function if this file is executed directly
if (require.main === module) {
  main().catch((error) => {
    logger.error("Fatal error during Redis testing:", error);
    throw error;
  });
}
