/**
 * Provider configuration routes
 * Provides information about available AI providers and their configuration
 */

import { Router } from "express";
import { apiKeyAuth } from "../middleware/auth";
import { asyncHandler } from "../middleware/errorHandler";
import { AIServiceFactory } from "../services/aiServiceFactory";
import logger from "../utils/logger";

const router = Router();

// Apply authentication to all provider routes
router.use(apiKeyAuth);

/**
 * GET /api/providers
 * Get all available AI providers and their configurations
 */
router.get("/", asyncHandler(async (req, res) => {
  logger.info("Fetching all AI providers");
  
  const providers = AIServiceFactory.getAllProviders();
  const configs = AIServiceFactory.getAllProvidersConfig();
  const health = await AIServiceFactory.checkAllProvidersHealth();

  const providersInfo = providers.map(provider => ({
    name: provider.name,
    models: provider.getAvailableModels(),
    config: configs[provider.name] || {},
    healthy: health[provider.name] || false,
  }));

  res.status(200).json({
    providers: providersInfo,
    total: providers.length,
    timestamp: new Date().toISOString(),
  });
}));

/**
 * GET /api/providers/:name
 * Get specific provider information
 */
router.get("/:name", asyncHandler(async (req, res) => {
  const { name } = req.params;
  const provider = AIServiceFactory.getProvider(name);

  if (!provider) {
    return res.status(404).json({
      error: "Provider not found",
      name,
    });
  }

  logger.info(`Fetching provider info for: ${name}`);

  const isHealthy = await provider.isHealthy();
  const config = provider.getConfig();
  const models = provider.getAvailableModels();

  res.status(200).json({
    name: provider.name,
    healthy: isHealthy,
    models,
    config,
    timestamp: new Date().toISOString(),
  });
}));

/**
 * GET /api/providers/:name/health
 * Check specific provider health
 */
router.get("/:name/health", asyncHandler(async (req, res) => {
  const { name } = req.params;
  const provider = AIServiceFactory.getProvider(name);

  if (!provider) {
    return res.status(404).json({
      error: "Provider not found",
      name,
    });
  }

  const isHealthy = await provider.isHealthy();

  res.status(isHealthy ? 200 : 503).json({
    name: provider.name,
    healthy: isHealthy,
    timestamp: new Date().toISOString(),
  });
}));

/**
 * GET /api/providers/tiers/:tier
 * Get provider for specific tier
 */
router.get("/tiers/:tier", asyncHandler(async (req, res) => {
  const { tier } = req.params;
  
  if (!["basic", "standard", "premium"].includes(tier)) {
    return res.status(400).json({
      error: "Invalid tier. Must be 'basic', 'standard', or 'premium'",
    });
  }

  const provider = AIServiceFactory.getProviderForTier(tier as "basic" | "standard" | "premium");
  const modelConfig = AIServiceFactory.getModelConfigForTier(tier as "basic" | "standard" | "premium");

  if (!provider || !modelConfig) {
    return res.status(404).json({
      error: "No provider configured for this tier",
      tier,
    });
  }

  const isHealthy = await provider.isHealthy();

  res.status(200).json({
    tier,
    provider: {
      name: provider.name,
      healthy: isHealthy,
      models: provider.getAvailableModels(),
    },
    modelConfig,
    timestamp: new Date().toISOString(),
  });
}));

export default router;
