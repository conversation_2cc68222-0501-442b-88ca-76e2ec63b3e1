import { Router } from "express";
import { statsController } from "../controllers/statsController";
import {
  getDetailedPerformanceStats,
  getSummaryStats,
} from "../services/statsService";
import { apiKeyAuth } from "../middleware/auth";

const router = Router();

// Apply authentication to all stats routes
router.use(apiKeyAuth);

/**
 * GET /api/stats/summary
 * Get summary statistics
 */
router.get("/summary", statsController.getSummaryStats);

/**
 * GET /api/stats/user/:userId
 * Get user-specific statistics
 */
router.get("/user/:userId", statsController.getUserStats);

/**
 * GET /api/stats/historical
 * Get historical statistics from database
 */
router.get("/historical", statsController.getHistoricalStats);

/**
 * GET /api/stats/combined
 * Get combined stats (recent from Redis + historical from database)
 */
router.get("/combined", statsController.getCombinedStats);

/**
 * GET /api/stats/health
 * Get health status of all services
 */
router.get("/health", statsController.getHealthStatus);

/**
 * GET /api/stats/performance
 * Get detailed performance statistics
 */
router.get("/performance", async (req, res) => {
  try {
    const stats = await getDetailedPerformanceStats();
    res.status(200).json(stats);
  } catch (error) {
    res.status(500).json({
      error: "Failed to fetch performance stats",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
});

/**
 * GET /api/stats/summary-detailed
 * Get detailed summary statistics
 */
router.get("/summary-detailed", async (req, res) => {
  try {
    const stats = await getSummaryStats();
    res.status(200).json(stats);
  } catch (error) {
    res.status(500).json({
      error: "Failed to fetch detailed summary stats",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
});

export default router;
