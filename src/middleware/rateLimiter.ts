import type { Request } from "express";
import rateLimit from "express-rate-limit";
import { cacheGet, cacheSet } from "../utils/redis";

/**
 * Rate limit cache entry interface
 */
interface RateLimitCacheEntry {
  count: number;
  resetTime: number;
}

/**
 * Create a rate limiter with Redis-backed storage
 */
export const createRateLimiter = (options: {
  windowMs: number;
  max: number;
  message: string;
  keyGenerator?: (req: Request) => string;
}) => {
  return rateLimit({
    windowMs: options.windowMs,
    max: options.max,
    message: { error: options.message },
    keyGenerator: options.keyGenerator || ((req: Request) => req.ip || "unknown"),
    standardHeaders: true,
    legacyHeaders: false,
    store: {
      incr: async (key: string): Promise<{ totalHits: number; resetTime?: Date }> => {
        const now = Date.now();
        
        try {
          // Get current count from cache
          const cached = await cacheGet(`ratelimit:${key}`);
          let entry: RateLimitCacheEntry;
          
          if (cached) {
            entry = JSON.parse(cached);
            // Reset if window has expired
            if (entry.resetTime <= now) {
              entry = { count: 0, resetTime: now + options.windowMs };
            }
          } else {
            entry = { count: 0, resetTime: now + options.windowMs };
          }
          
          // Increment count
          entry.count++;
          
          // Save back to cache
          const ttl = Math.ceil((entry.resetTime - now) / 1000);
          await cacheSet(`ratelimit:${key}`, JSON.stringify(entry), ttl);
          
          return {
            totalHits: entry.count,
            resetTime: new Date(entry.resetTime),
          };
        } catch {
          // Fallback to allowing the request if cache fails
          return { totalHits: 1 };
        }
      },
      
      decrement: async (key: string): Promise<void> => {
        try {
          const cached = await cacheGet(`ratelimit:${key}`);
          if (cached) {
            const entry: RateLimitCacheEntry = JSON.parse(cached);
            entry.count = Math.max(0, entry.count - 1);
            
            const now = Date.now();
            const ttl = Math.ceil((entry.resetTime - now) / 1000);
            await cacheSet(`ratelimit:${key}`, JSON.stringify(entry), ttl);
          }
        } catch {
          // Ignore errors in decrement
        }
      },
      
      resetKey: async (key: string): Promise<void> => {
        try {
          const now = Date.now();
          const entry: RateLimitCacheEntry = {
            count: 0,
            resetTime: now + options.windowMs,
          };
          
          const ttl = Math.ceil(options.windowMs / 1000);
          await cacheSet(`ratelimit:${key}`, JSON.stringify(entry), ttl);
        } catch {
          // Ignore errors in reset
        }
      },
    },
  });
};

/**
 * API Key specific rate limiter
 */
export const apiKeyRateLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each API key to 100 requests per windowMs
  message: "Too many requests from this API key, please try again later.",
  keyGenerator: (req: Request) => {
    const apiKey = req.headers["x-api-key"] as string || 
                   req.headers["authorization"]?.replace("Bearer ", "") ||
                   req.query.apiKey as string;
    return apiKey || req.ip || "unknown";
  },
});

/**
 * Filter endpoint specific rate limiter
 */
export const filterRateLimiter = createRateLimiter({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 60, // Limit each IP to 60 filter requests per minute
  message: "Too many filter requests, please try again later.",
});
