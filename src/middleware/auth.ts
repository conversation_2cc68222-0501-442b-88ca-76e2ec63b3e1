import type { NextFunction, Request, Response } from "express";
import { validateApiKey } from "../services/apiKeyService";
import { AppError } from "./errorHandler";

// Extend Express Request interface to include API key information
declare global {
  namespace Express {
    interface Request {
      apiKey?: {
        key: string;
        userId: string;
        createdAt: Date;
        lastUsedAt: Date | null;
        isActive: boolean;
      };
      userId?: string;
    }
  }
}

/**
 * API Key Authentication Middleware
 * Validates API keys and attaches user information to the request
 */
export const apiKeyAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Extract API key from headers
    const apiKey = req.headers["x-api-key"] as string || 
                   req.headers["authorization"]?.replace("Bearer ", "") ||
                   req.query.apiKey as string;

    if (!apiKey) {
      throw new AppError("API key is required", 401);
    }

    // Validate the API key
    const validatedKey = await validateApiKey(apiKey);

    if (!validatedKey) {
      throw new AppError("Invalid or expired API key", 401);
    }

    // Check if the API key is active
    if (!validatedKey.isActive) {
      throw new AppError("API key has been revoked", 401);
    }

    // Attach API key information to the request
    req.apiKey = validatedKey;
    req.userId = validatedKey.userId;

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Optional API Key Authentication Middleware
 * Validates API keys if present, but doesn't require them
 */
export const optionalApiKeyAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Extract API key from headers
    const apiKey = req.headers["x-api-key"] as string || 
                   req.headers["authorization"]?.replace("Bearer ", "") ||
                   req.query.apiKey as string;

    if (apiKey) {
      // Validate the API key if present
      const validatedKey = await validateApiKey(apiKey);

      if (validatedKey && validatedKey.isActive) {
        // Attach API key information to the request
        req.apiKey = validatedKey;
        req.userId = validatedKey.userId;
      }
    }

    // If no API key or invalid key, use IP-based identification
    if (!req.userId) {
      req.userId = req.ip || "anonymous";
    }

    next();
  } catch {
    // For optional auth, we don't fail on errors, just continue without auth
    req.userId = req.ip || "anonymous";
    next();
  }
};
