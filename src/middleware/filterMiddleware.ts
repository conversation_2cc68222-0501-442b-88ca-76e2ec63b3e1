import type { NextFunction, Request, Response } from "express";
import { AppError, async<PERSON>and<PERSON> } from "./errorHandler";
import {
  type FilterRequest,
  type FilterResponse,
  filterContent,
  validateFilterConfig,
} from "../services/filterService";

/**
 * Process filter request middleware
 * Handles the actual filtering logic
 */
export const processFilterRequest = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { text = "", image, config, oldMessages = [], model } = req.body;

      // Validate config to ensure all flags default to false
      const validatedConfig = validateFilterConfig(config);

      // Create filter request
      const filterRequest: FilterRequest = {
        text,
        image,
        config: validatedConfig,
        oldMessages,
        model,
      };

      // Process filter request
      const result = await filterContent(
        filterRequest,
        req.userId || "anonymous"
      );

      // Attach result to request for further processing
      req.filterResult = result;

      next();
    } catch (error) {
      next(error);
    }
  }
);

/**
 * Send filter response middleware
 * Sends the filter result as JSON response
 */
export const sendFilterResponse = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  try {
    if (!req.filterResult) {
      throw new AppError("No filter result available", 500);
    }

    res.status(200).json(req.filterResult);
  } catch (error) {
    next(error);
  }
};

// Extend Express Request interface to include filter result
declare global {
  namespace Express {
    interface Request {
      filterResult?: FilterResponse;
    }
  }
}
