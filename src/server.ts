import createApp from "./app";
import { warmupConnectionPool } from "./db";
import {
  closeRedisConnection,
  getRedisClient,
  isRedisHealthy,
} from "./utils/redis";
import logger from "./utils/logger";

const PORT = process.env.PORT || 3000;
const NODE_ENV = process.env.NODE_ENV || "development";

/**
 * Start the server
 */
const startServer = async (): Promise<void> => {
  try {
    logger.info("Starting SanityAI Content Moderation API...");

    // Warm up database connections
    logger.info("Warming up database connections...");
    await warmupConnectionPool();

    // Check Redis connection
    const redisHealthy = await isRedisHealthy();
    if (redisHealthy) {
      logger.info("Redis connection established");
    } else {
      logger.warn("Redis not available, using memory cache fallback");
    }

    // Create Express app
    const app = createApp();

    // Start server
    const server = app.listen(PORT, () => {
      logger.info(`Server running on port ${PORT} in ${NODE_ENV} mode`);
      logger.info(`Health check available at: http://localhost:${PORT}/health`);
      logger.info(`API documentation: http://localhost:${PORT}/api/docs`);
    });

    // Graceful shutdown handling
    const gracefulShutdown = async (signal: string): Promise<void> => {
      logger.info(`Received ${signal}. Starting graceful shutdown...`);

      // Close server
      server.close(async () => {
        logger.info("HTTP server closed");

        try {
          // Close Redis connection
          await closeRedisConnection();
          logger.info("Redis connection closed");

          logger.info("Graceful shutdown completed");
          process.exit(0);
        } catch (error) {
          logger.error("Error during graceful shutdown:", error);
          process.exit(1);
        }
      });

      // Force close after 30 seconds
      setTimeout(() => {
        logger.error(
          "Could not close connections in time, forcefully shutting down"
        );
        process.exit(1);
      }, 30000);
    };

    // Handle shutdown signals
    process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
    process.on("SIGINT", () => gracefulShutdown("SIGINT"));

    // Handle uncaught exceptions
    process.on("uncaughtException", (error: Error) => {
      logger.error("Uncaught Exception:", error);
      gracefulShutdown("uncaughtException");
    });

    // Handle unhandled promise rejections
    process.on(
      "unhandledRejection",
      (reason: unknown, promise: Promise<unknown>) => {
        logger.error(`Unhandled Rejection at: ${promise}, reason: ${reason}`);
        gracefulShutdown("unhandledRejection");
      }
    );

    // Log Redis client status periodically
    setInterval(async () => {
      const redisClient = getRedisClient();
      if (redisClient) {
        try {
          await redisClient.ping();
          logger.debug("Redis health check: OK");
        } catch (error) {
          logger.warn("Redis health check failed:", error);
        }
      }
    }, 60000); // Check every minute
  } catch (error) {
    logger.error("Failed to start server:", error);
    throw error;
  }
};

// Start the server
if (require.main === module) {
  startServer().catch((error) => {
    logger.error("Server startup failed:", error);
    process.exit(1);
  });
}

export { startServer };
