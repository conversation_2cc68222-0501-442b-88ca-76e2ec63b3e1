import express, { type Express, type Request, type Response } from "express";
import cors from "cors";
import helmet from "helmet";
import compression from "compression";
import rateLimit from "express-rate-limit";

import { config } from "./config";
import { errorHandler, notFoundHandler } from "./middleware/errorHandler";
import { apiKeyRateLimiter } from "./middleware/rateLimiter";

// Import routes
import apiKeyRoutes from "./routes/apiKey";
import filterRoutes from "./routes/filter";
import providersRoutes from "./routes/providers";
import statsRoutes from "./routes/statsRoutes";

/**
 * Create Express application
 */
const createApp = (): Express => {
  const app = express();

  // Security middleware
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
    crossOriginEmbedderPolicy: false,
  }));

  // CORS configuration
  app.use(cors({
    origin: config.cors.origin,
    credentials: config.cors.credentials,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization", "X-API-Key"],
  }));

  // Compression middleware
  app.use(compression());

  // Body parsing middleware
  app.use(express.json({ 
    limit: config.server.maxRequestSize,
    strict: true,
  }));
  app.use(express.urlencoded({ 
    extended: true, 
    limit: config.server.maxRequestSize,
  }));

  // Global rate limiting
  const globalRateLimit = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 1000, // Limit each IP to 1000 requests per windowMs
    message: {
      error: "Too many requests from this IP, please try again later.",
    },
    standardHeaders: true,
    legacyHeaders: false,
  });
  app.use(globalRateLimit);

  // Trust proxy for accurate IP addresses
  app.set("trust proxy", 1);

  // Health check endpoint
  app.get("/health", (_req: Request, res: Response) => {
    res.status(200).json({
      status: "healthy",
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || "1.0.0",
    });
  });

  // API routes with rate limiting
  app.use("/api/keys", apiKeyRateLimiter, apiKeyRoutes);
  app.use("/api/filter", filterRoutes);
  app.use("/api/providers", providersRoutes);
  app.use("/api/stats", statsRoutes);

  // Root endpoint
  app.get("/", (_req: Request, res: Response) => {
    res.status(200).json({
      message: "SanityAI Content Moderation API",
      version: process.env.npm_package_version || "1.0.0",
      documentation: "/api/docs",
      health: "/health",
    });
  });

  // Error handling middleware (must be last)
  app.use(notFoundHandler);
  app.use(errorHandler);

  return app;
};

export default createApp;
