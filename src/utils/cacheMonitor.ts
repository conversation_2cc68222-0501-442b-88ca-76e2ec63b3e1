import logger from "./logger";

/**
 * Cache metrics interface
 */
export interface CacheMetrics {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  evictions: number;
  size: number;
  memoryUsage: number;
  timestamp: number;
}

/**
 * Cache health interface
 */
export interface CacheHealth {
  status: "healthy" | "degraded" | "unhealthy";
  hitRate: number;
  evictionRate: number;
  memoryPressure: number;
  recommendations: string[];
}

/**
 * Cache Monitor
 * Monitors cache performance and provides health insights
 */
export class CacheMonitor {
  private metrics: CacheMetrics[] = [];
  private maxMetricsHistory = 1000;
  private monitoringInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.startMonitoring();
  }

  /**
   * Start monitoring cache metrics
   */
  startMonitoring(intervalMs: number = 60000): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    this.monitoringInterval = setInterval(() => {
      this.collectMetrics();
    }, intervalMs);

    logger.info("Cache monitoring started");
  }

  /**
   * Stop monitoring
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    logger.info("Cache monitoring stopped");
  }

  /**
   * Collect current cache metrics
   */
  collectMetrics(): void {
    try {
      const metrics: CacheMetrics = {
        hits: 0,
        misses: 0,
        sets: 0,
        deletes: 0,
        evictions: 0,
        size: 0,
        memoryUsage: 0,
        timestamp: Date.now(),
      };

      // Add to metrics history
      this.addMetric(metrics);
    } catch (error) {
      logger.error("Error collecting cache metrics:", error);
    }
  }

  /**
   * Add a metric to the history
   */
  addMetric(metric: CacheMetrics): void {
    this.metrics.push(metric);

    // Keep only the last N metrics
    if (this.metrics.length > this.maxMetricsHistory) {
      this.metrics = this.metrics.slice(-this.maxMetricsHistory);
    }
  }

  /**
   * Get cache health assessment
   */
  getCacheHealth(): CacheHealth {
    const recentMetrics = this.getRecentMetrics(10);

    if (recentMetrics.length === 0) {
      return {
        status: "unhealthy",
        hitRate: 0,
        evictionRate: 0,
        memoryPressure: 0,
        recommendations: ["No metrics available"],
      };
    }

    const totalHits = recentMetrics.reduce((sum, m) => sum + m.hits, 0);
    const totalMisses = recentMetrics.reduce((sum, m) => sum + m.misses, 0);
    const totalRequests = totalHits + totalMisses;
    const hitRate = totalRequests > 0 ? totalHits / totalRequests : 0;

    const evictionRate = this.getRecentEvictionRate();

    const avgMemoryUsage =
      recentMetrics.reduce((sum, m) => sum + m.memoryUsage, 0) /
      recentMetrics.length;
    const memoryPressure = avgMemoryUsage / (1024 * 1024 * 100); // Normalize to 100MB

    let status: "healthy" | "degraded" | "unhealthy" = "healthy";
    const recommendations: string[] = [];

    // Assess health based on metrics
    if (hitRate < 0.5) {
      status = "degraded";
      recommendations.push(
        "Low hit rate detected. Consider increasing cache size or TTL."
      );
    }

    if (evictionRate > 0.1) {
      status = "degraded";
      recommendations.push(
        "High eviction rate detected. Consider increasing cache capacity."
      );
    }

    if (memoryPressure > 0.8) {
      status = "unhealthy";
      recommendations.push(
        "High memory pressure. Consider reducing cache size or implementing compression."
      );
    }

    if (hitRate < 0.3 || evictionRate > 0.2) {
      status = "unhealthy";
    }

    return {
      status,
      hitRate,
      evictionRate,
      memoryPressure,
      recommendations,
    };
  }

  /**
   * Get recent eviction rate
   */
  getRecentEvictionRate(): number {
    const recentMetrics = this.getRecentMetrics(5);

    if (recentMetrics.length < 2) {
      return 0;
    }

    const totalEvictions = recentMetrics.reduce(
      (sum, m) => sum + m.evictions,
      0
    );
    const totalSets = recentMetrics.reduce((sum, m) => sum + m.sets, 0);

    return totalSets > 0 ? totalEvictions / totalSets : 0;
  }

  /**
   * Get performance trends
   */
  getPerformanceTrends(): Record<string, "improving" | "stable" | "degrading"> {
    const trends: Record<string, "improving" | "stable" | "degrading"> = {};

    trends.hits = this.getTrend("hits");
    trends.evictions = this.getTrend("evictions");
    trends.memoryUsage = this.getTrend("memoryUsage");

    return trends;
  }

  /**
   * Get trend for a specific metric
   */
  private getTrend(
    metric: keyof CacheMetrics
  ): "improving" | "stable" | "degrading" {
    const recentMetrics = this.getRecentMetrics(10);

    if (recentMetrics.length < 5) {
      return "stable";
    }

    const firstHalf = recentMetrics.slice(
      0,
      Math.floor(recentMetrics.length / 2)
    );
    const secondHalf = recentMetrics.slice(
      Math.floor(recentMetrics.length / 2)
    );

    const firstAvg =
      firstHalf.reduce((sum, m) => sum + (m[metric] as number), 0) /
      firstHalf.length;
    const secondAvg =
      secondHalf.reduce((sum, m) => sum + (m[metric] as number), 0) /
      secondHalf.length;

    const change = (secondAvg - firstAvg) / firstAvg;

    if (Math.abs(change) < 0.05) {
      return "stable";
    }

    // For hit rate, higher is better
    if (metric === "hits") {
      return change > 0 ? "improving" : "degrading";
    }

    // For evictions and memory usage, lower is better
    return change < 0 ? "improving" : "degrading";
  }

  /**
   * Get optimization recommendations
   */
  getOptimizationRecommendations(): string[] {
    const health = this.getCacheHealth();
    const trends = this.getPerformanceTrends();
    const recommendations: string[] = [...health.recommendations];

    if (trends.hits === "degrading") {
      recommendations.push(
        "Hit rate is declining. Review cache key strategies and TTL settings."
      );
    }

    if (trends.evictions === "degrading") {
      recommendations.push(
        "Eviction rate is increasing. Consider cache size optimization."
      );
    }

    if (trends.memoryUsage === "degrading") {
      recommendations.push(
        "Memory usage is growing. Implement cache cleanup strategies."
      );
    }

    return [...new Set(recommendations)]; // Remove duplicates
  }

  /**
   * Get recent metrics
   */
  private getRecentMetrics(count: number): CacheMetrics[] {
    return this.metrics.slice(-count);
  }

  /**
   * Get default metrics
   */
  private getDefaultMetrics(): CacheMetrics {
    return {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      evictions: 0,
      size: 0,
      memoryUsage: 0,
      timestamp: Date.now(),
    };
  }

  /**
   * Export metrics for external analysis
   */
  exportMetrics(): CacheMetrics[] {
    return [...this.metrics];
  }

  /**
   * Clear metrics history
   */
  clearMetrics(): void {
    this.metrics = [];
    logger.info("Cache metrics cleared");
  }

  /**
   * Get summary statistics
   */
  getSummaryStats(): Record<string, unknown> {
    const recentMetrics = this.getRecentMetrics(100);

    if (recentMetrics.length === 0) {
      return {
        totalMetrics: 0,
        avgHitRate: 0,
        avgEvictionRate: 0,
        avgMemoryUsage: 0,
      };
    }

    const totalHits = recentMetrics.reduce((sum, m) => sum + m.hits, 0);
    const totalMisses = recentMetrics.reduce((sum, m) => sum + m.misses, 0);
    const totalRequests = totalHits + totalMisses;
    const avgHitRate = totalRequests > 0 ? totalHits / totalRequests : 0;

    const avgEvictionRate = this.getRecentEvictionRate();
    const avgMemoryUsage =
      recentMetrics.reduce((sum, m) => sum + m.memoryUsage, 0) /
      recentMetrics.length;

    return {
      totalMetrics: recentMetrics.length,
      avgHitRate,
      avgEvictionRate,
      avgMemoryUsage,
      health: this.getCacheHealth(),
      trends: this.getPerformanceTrends(),
    };
  }
}

// Export singleton instance
export const cacheMonitor = new CacheMonitor();
