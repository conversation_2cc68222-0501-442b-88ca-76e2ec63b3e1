import Redis from "ioredis";
import logger from "./logger";

// Redis client instance
let redisClientInstance: Redis | null = null;
let isRedisAvailable = false;

// Memory cache fallback implementation
class MemoryCacheImpl {
  private cache = new Map<string, string>();
  private hashCache = new Map<string, Map<string, string>>();
  private listCache = new Map<string, string[]>();

  async get(key: string): Promise<string | null> {
    return this.cache.get(key) || null;
  }

  async set(key: string, value: string, ttl?: number): Promise<void> {
    this.cache.set(key, value);
    if (ttl) {
      setTimeout(() => this.cache.delete(key), ttl * 1000);
    }
  }

  async del(key: string): Promise<number> {
    if (this.cache.has(key)) {
      this.cache.delete(key);
      return 1;
    }
    return 0;
  }

  async incr(key: string): Promise<number> {
    const current = parseInt(this.cache.get(key) || "0", 10);
    const newValue = current + 1;
    this.cache.set(key, newValue.toString());
    return newValue;
  }

  async incrby(key: string, increment: number): Promise<number> {
    const current = parseInt(this.cache.get(key) || "0", 10);
    const newValue = current + increment;
    this.cache.set(key, newValue.toString());
    return newValue;
  }

  async mget(keys: string[]): Promise<Array<string | null>> {
    return keys.map(key => this.cache.get(key) || null);
  }

  async hgetall(key: string): Promise<Record<string, string>> {
    const hash = this.hashCache.get(key);
    if (!hash) {
      return {};
    }
    const result: Record<string, string> = {};
    hash.forEach((value, field) => {
      result[field] = value;
    });
    return result;
  }

  async lrange(key: string, start: number, stop: number): Promise<string[]> {
    const list = this.listCache.get(key) || [];
    return list.slice(start, stop === -1 ? undefined : stop + 1);
  }

  async llen(key: string): Promise<number> {
    const list = this.listCache.get(key) || [];
    return list.length;
  }

  pipeline(): {
    incr: (key: string) => void;
    incrby: (key: string, increment: number) => void;
    exec: () => Promise<Array<[Error | null, unknown]>>;
  } {
    const commands: Array<() => Promise<unknown>> = [];
    
    return {
      incr: (key: string) => {
        commands.push(() => this.incr(key));
      },
      incrby: (key: string, increment: number) => {
        commands.push(() => this.incrby(key, increment));
      },
      exec: async () => {
        const results: Array<[Error | null, unknown]> = [];
        for (const command of commands) {
          try {
            const result = await command();
            results.push([null, result]);
          } catch (error) {
            results.push([error as Error, null]);
          }
        }
        return results;
      }
    };
  }

  async ping(): Promise<string> {
    return "PONG";
  }
}

// Memory cache fallback
const memoryCache = new MemoryCacheImpl();

/**
 * Get Redis client instance
 */
export const getRedisClient = (): Redis | null => {
  if (!isRedisAvailable) {
    return null;
  }
  return redisClientInstance;
};

// Cache operations with fallback
export const cacheGet = async (key: string): Promise<string | null> => {
  try {
    if (isRedisAvailable && redisClientInstance) {
      return await redisClientInstance.get(key);
    }
    return await memoryCache.get(key);
  } catch (error) {
    logger.error("Cache get error:", error);
    return await memoryCache.get(key);
  }
};

export const cacheSet = async (
  key: string,
  value: string,
  ttl?: number
): Promise<void> => {
  try {
    if (isRedisAvailable && redisClientInstance) {
      if (ttl) {
        await redisClientInstance.setex(key, ttl, value);
      } else {
        await redisClientInstance.set(key, value);
      }
    } else {
      await memoryCache.set(key, value, ttl);
    }
  } catch (error) {
    logger.error("Cache set error:", error);
    await memoryCache.set(key, value, ttl);
  }
};

export const cacheDelete = async (key: string): Promise<number> => {
  try {
    if (isRedisAvailable && redisClientInstance) {
      return await redisClientInstance.del(key);
    }
    return await memoryCache.del(key);
  } catch (error) {
    logger.error("Cache delete error:", error);
    return await memoryCache.del(key);
  }
};

// Stats operations
export const statsIncrement = async (key: string, increment: number = 1): Promise<number> => {
  try {
    if (isRedisAvailable && redisClientInstance) {
      if (increment === 1) {
        return await redisClientInstance.incr(key);
      } else {
        return await redisClientInstance.incrby(key, increment);
      }
    }
    if (increment === 1) {
      return await memoryCache.incr(key);
    } else {
      return await memoryCache.incrby(key, increment);
    }
  } catch (error) {
    logger.error("Stats increment error:", error);
    if (increment === 1) {
      return await memoryCache.incr(key);
    } else {
      return await memoryCache.incrby(key, increment);
    }
  }
};

export const statsGet = async (key: string): Promise<string | null> => {
  try {
    if (isRedisAvailable && redisClientInstance) {
      return await redisClientInstance.get(key);
    }
    return await memoryCache.get(key);
  } catch (error) {
    logger.error("Stats get error:", error);
    return await memoryCache.get(key);
  }
};

export const statsGetMulti = async (keys: string[]): Promise<Array<string | null>> => {
  try {
    if (isRedisAvailable && redisClientInstance) {
      return await redisClientInstance.mget(...keys);
    }
    return await memoryCache.mget(keys);
  } catch (error) {
    logger.error("Stats get multi error:", error);
    return await memoryCache.mget(keys);
  }
};

export const statsPipeline = () => {
  try {
    if (isRedisAvailable && redisClientInstance) {
      return redisClientInstance.pipeline();
    }
    return memoryCache.pipeline();
  } catch (error) {
    logger.error("Stats pipeline error:", error);
    return memoryCache.pipeline();
  }
};

export const statsHGetAll = async (key: string): Promise<Record<string, string>> => {
  try {
    if (isRedisAvailable && redisClientInstance) {
      return await redisClientInstance.hgetall(key);
    }
    return await memoryCache.hgetall(key);
  } catch (error) {
    logger.error("Stats hgetall error:", error);
    return await memoryCache.hgetall(key);
  }
};

export const statsLRange = async (
  key: string,
  start: number,
  stop: number
): Promise<string[]> => {
  try {
    if (isRedisAvailable && redisClientInstance) {
      return await redisClientInstance.lrange(key, start, stop);
    }
    return await memoryCache.lrange(key, start, stop);
  } catch (error) {
    logger.error("Stats lrange error:", error);
    return await memoryCache.lrange(key, start, stop);
  }
};

export const statsLLen = async (key: string): Promise<number> => {
  try {
    if (isRedisAvailable && redisClientInstance) {
      return await redisClientInstance.llen(key);
    }
    return await memoryCache.llen(key);
  } catch (error) {
    logger.error("Stats llen error:", error);
    return await memoryCache.llen(key);
  }
};

/**
 * Check if Redis is healthy
 */
export const isRedisHealthy = async (): Promise<boolean> => {
  try {
    if (isRedisAvailable && redisClientInstance) {
      const result = await redisClientInstance.ping();
      return result === "PONG";
    }
    return false;
  } catch (error) {
    logger.error("Redis health check failed:", error);
    return false;
  }
};

/**
 * Close Redis connection
 */
export const closeRedisConnection = async (): Promise<void> => {
  if (redisClientInstance) {
    try {
      await redisClientInstance.quit();
      redisClientInstance = null;
      isRedisAvailable = false;
      logger.info("Redis connection closed");
    } catch (error) {
      logger.error("Error closing Redis connection:", error);
    }
  }
};

/**
 * Initialize Redis connection
 */
const initializeRedis = (): Promise<void> => {
  return new Promise((resolve) => {
    try {
      const redisUrl = process.env.REDIS_URL || "redis://localhost:6379";
      
      redisClientInstance = new Redis(redisUrl, {
        maxRetriesPerRequest: 3,
        lazyConnect: true,
        connectTimeout: 5000,
        commandTimeout: 5000,
      });

      redisClientInstance.on("connect", () => {
        logger.info("Redis connected successfully");
        isRedisAvailable = true;
        resolve();
      });

      redisClientInstance.on("error", (error) => {
        logger.warn("Redis connection error, falling back to memory cache:", error);
        isRedisAvailable = false;
        if (redisClientInstance) {
          redisClientInstance.disconnect();
          redisClientInstance = null;
        }
        resolve();
      });

      redisClientInstance.on("close", () => {
        logger.warn("Redis connection closed, using memory cache");
        isRedisAvailable = false;
      });

      // Attempt to connect
      redisClientInstance.connect().catch((error) => {
        logger.warn("Failed to connect to Redis, using memory cache:", error);
        isRedisAvailable = false;
        resolve();
      });

    } catch (error) {
      logger.warn("Redis initialization failed, using memory cache:", error);
      isRedisAvailable = false;
      resolve();
    }
  });
};

// Initialize Redis on module load
initializeRedis().catch((error) => {
  logger.error("Failed to initialize Redis:", error);
});

// Export clients and utilities for direct access when needed
export { redisClientInstance, isRedisAvailable };
