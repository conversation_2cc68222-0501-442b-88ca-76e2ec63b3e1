/**
 * Google Gemini API service for content filtering
 * Implements the same interface as akashChatService for consistent behavior
 */

import { GoogleGenAI } from "@google/genai";
import { config } from "../config";
import type {
  AIAnalysisResult,
  AIMessage,
  AIProvider,
} from "../interfaces/aiProvider";
import {
  createSystemPrompt,
  formatMessageHistory,
  parseAiResponse,
} from "../utils/aiProviderUtils";
import { isAIReviewNeeded } from "./akashChatService";
import {
  generateAICacheKey,
  getCachedResponse,
  setCachedResponse,
} from "../utils/cache";
import { statsIncrement } from "../utils/redis";
import { trackApiResponseTime } from "../utils/apiResponseTime";

/**
 * Gemini AI Provider implementation
 */
export class GeminiProvider implements AIProvider {
  readonly name = "gemini";
  private client: GoogleGenAI;

  constructor() {
    this.client = new GoogleGenAI({
      apiKey: config.gemini.apiKey,
    });
  }

  /**
   * Analyze text content through Google Gemini API
   * @param text Text to analyze
   * @param oldMessages Previous messages for context
   * @param filterConfig Configuration for content filtering
   * @param modelName Specific Gemini model to use
   * @returns Analysis result with flags, reasoning, and filtered content
   */
  async analyzeTextContent(
    text: string,
    oldMessages: any[] = [],
    filterConfig: Record<string, boolean> = {},
    modelName: string
  ): Promise<AIAnalysisResult> {
    // First check if AI review is even needed (same pre-screening as Akash)
    const prescreeningResult = isAIReviewNeeded(text, filterConfig);

    if (!prescreeningResult.needsReview) {
      console.log(
        `[Gemini Analysis] Pre-screening determined AI review not needed, returning safe result`
      );

      // Handle post-response stats in background
      setImmediate(async () => {
        try {
          await statsIncrement("ai:prescreening:skipped");
        } catch (error) {
          console.error(
            "[Gemini Analysis] Error tracking prescreening stats:",
            error
          );
        }
      });

      return {
        isViolation: false,
        flags: [],
        reason: "Content passed all moderation checks",
        filteredContent: filterConfig.returnFilteredMessage ? text : undefined,
      };
    }

    // Check cache first
    const cacheKey = generateAICacheKey(text, filterConfig, oldMessages);

    try {
      const cachedResult = await getCachedResponse(cacheKey);
      if (cachedResult) {
        console.log(`[Gemini Analysis] Cache hit for key: ${cacheKey}`);
        return cachedResult;
      }
    } catch (error) {
      console.log(`[Gemini Analysis] Cache check failed:`, error);
    }

    try {
      // Format previous messages for context
      const messageHistory = formatMessageHistory(oldMessages, text);

      // Create prompt for content moderation (same as Akash)
      const systemPrompt = createSystemPrompt(filterConfig);
      console.log(
        `[Gemini Analysis] Using system prompt length: ${systemPrompt.length} chars`
      );

      // Convert messages to Gemini format
      const geminiContents = this.convertToGeminiFormat(
        messageHistory,
        systemPrompt
      );

      // Track API call starting time for performance monitoring
      const apiCallStartTime = Date.now();

      // Make API request to Gemini
      console.log(
        `[Gemini Analysis] Sending request to Gemini API with ${geminiContents.length} messages using model: ${modelName}`
      );

      const response = await this.client.models.generateContent({
        model: modelName,
        config: {
          responseMimeType: "text/plain",
          temperature: 0.1, // Lower temperature for consistent responses
          maxOutputTokens: 300, // Reduced token count for faster response
        },
        contents: geminiContents,
      });

      // Calculate API call duration for monitoring
      const apiCallDuration = Date.now() - apiCallStartTime;
      console.log(
        `[Gemini Analysis] API call completed in ${apiCallDuration}ms`
      );

      // Track API call performance IMMEDIATELY
      try {
        await trackApiResponseTime("text", apiCallDuration, false, false);
        console.log(
          `[Gemini Analysis] API stats tracked successfully: ${apiCallDuration}ms`
        );
      } catch (error) {
        console.error(
          "[Gemini Analysis] Error tracking API performance:",
          error
        );
      }

      // Track additional stats in background
      setImmediate(async () => {
        try {
          await statsIncrement("ai:api:total_time", apiCallDuration);
          await statsIncrement("ai:api:call_count");
          await statsIncrement("gemini:api:call_count");
        } catch (error) {
          console.error(
            "[Gemini Analysis] Error tracking additional stats:",
            error
          );
        }
      });

      // Parse the response
      const aiResponse = response.text || "";
      console.log(
        `[Gemini Analysis] Received response of length: ${aiResponse.length} chars`
      );
      console.log(
        `[Gemini Analysis] Raw AI response preview: "${aiResponse.substring(
          0,
          100
        )}..."`
      );

      const result = parseAiResponse(aiResponse);
      console.log(
        `[Gemini Analysis] Parsed result - isViolation: ${
          result.isViolation
        }, flags: [${result.flags.join(", ")}]`
      );

      // Cache the result in background
      setImmediate(async () => {
        try {
          await setCachedResponse(cacheKey, result);
          console.log(`[Gemini Analysis] Result cached with key: ${cacheKey}`);
        } catch (error) {
          console.error("[Gemini Analysis] Error caching result:", error);
        }
      });

      return result;
    } catch (error) {
      console.error("Error calling Gemini API:", error);

      // Track API errors IMMEDIATELY
      try {
        const errorDuration = 0;
        await trackApiResponseTime("text", errorDuration, true, false);
        console.log(`[Gemini Analysis] API error stats tracked successfully`);
      } catch (statsError) {
        console.error(
          "[Gemini Analysis] Error tracking API error stats:",
          statsError
        );
      }

      // Track additional error stats in background
      setImmediate(async () => {
        try {
          await statsIncrement("ai:api:errors");
          await statsIncrement("gemini:api:errors");
        } catch (error) {
          console.error(
            "[Gemini Analysis] Error tracking additional error stats:",
            error
          );
        }
      });

      // Return safe fallback
      return {
        isViolation: false,
        flags: [],
        reason: "AI analysis temporarily unavailable, content allowed",
      };
    }
  }

  /**
   * Convert message history to Gemini API format
   * @param messages Formatted message history
   * @param systemPrompt System prompt
   * @returns Gemini-compatible contents array
   */
  private convertToGeminiFormat(messages: AIMessage[], systemPrompt: string) {
    const contents = [];

    // Add system prompt as the first user message
    contents.push({
      role: "user",
      parts: [{ text: systemPrompt }],
    });

    // Add conversation history
    for (const message of messages) {
      if (message.role === "system") {continue;} // Skip system messages in history

      contents.push({
        role: message.role === "user" ? "user" : "user", // Gemini uses 'user' and 'model'
        parts: [{ text: message.content }],
      });
    }

    return contents;
  }

  /**
   * Check if Gemini service is healthy
   * @returns Promise resolving to health status
   */
  async isHealthy(): Promise<boolean> {
    try {
      // Simple health check with minimal request
      const response = await this.client.models.generateContent({
        model: "gemini-2.5-flash",
        config: {
          responseMimeType: "text/plain",
          maxOutputTokens: 10,
        },
        contents: [
          {
            role: "user",
            parts: [{ text: "Health check" }],
          },
        ],
      });

      return !!response.text;
    } catch (error) {
      console.error("[Gemini] Health check failed:", error);
      return false;
    }
  }

  /**
   * Get available Gemini models
   * @returns Array of model names
   */
  getAvailableModels(): string[] {
    return [
      "gemini-2.0-flash-exp",
      "gemini-2.5-flash",
      "gemini-1.5-pro",
      "gemini-1.5-flash",
    ];
  }

  /**
   * Get Gemini provider configuration
   * @returns Provider configuration
   */
  getConfig(): Record<string, any> {
    return {
      provider: this.name,
      baseUrl: config.gemini.baseUrl,
      timeout: config.gemini.timeout,
      hasApiKey: !!config.gemini.apiKey,
      availableModels: this.getAvailableModels(),
    };
  }
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use GeminiProvider class instead
 */
export const analyzeTextContentWithGemini = async (
  text: string,
  oldMessages: any[] = [],
  filterConfig: Record<string, boolean> = {},
  modelName: string = "gemini-2.5-flash"
): Promise<AIAnalysisResult> => {
  const provider = new GeminiProvider();
  return provider.analyzeTextContent(
    text,
    oldMessages,
    filterConfig,
    modelName
  );
};
