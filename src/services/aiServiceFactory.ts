import type { AIProvider, ModelTierConfig } from "../interfaces/aiProvider";
import { AkashChatProvider } from "./akashChatProvider";
import { GeminiProvider } from "./geminiService";
import logger from "../utils/logger";

/**
 * AI Provider Factory Interface
 */
interface AIProviderFactory {
  getProvider(name: string): AIProvider | null;
  getProviderForTier(tier: "basic" | "standard" | "premium"): AIProvider | null;
  getModelConfigForTier(
    tier: "basic" | "standard" | "premium"
  ): ModelTierConfig | null;
  getAllProviders(): AIProvider[];
  checkAllProvidersHealth(): Promise<Record<string, boolean>>;
  getAllProvidersConfig(): Record<string, Record<string, unknown>>;
  clearProviderCache(): void;
  validateConfiguration(): boolean;
  getProviderStats(): Record<string, unknown>;
}

/**
 * AI Service Factory
 * Manages multiple AI providers and provides a unified interface
 */
class AIServiceFactoryImpl implements AIProviderFactory {
  private providers: Map<string, AIProvider> = new Map();
  private modelTiers: Map<string, ModelTierConfig> = new Map();

  constructor() {
    this.initializeProviders();
    this.initializeModelTiers();
  }

  /**
   * Initialize all available providers
   */
  private initializeProviders(): void {
    try {
      // Initialize Akash Chat provider
      const akashProvider = new AkashChatProvider();
      this.providers.set("akash", akashProvider);

      // Initialize Gemini provider
      const geminiProvider = new GeminiProvider();
      this.providers.set("gemini", geminiProvider);

      logger.info(`Initialized ${this.providers.size} AI providers`);
    } catch (error) {
      logger.error("Error initializing providers:", error);
    }
  }

  /**
   * Initialize model tier configurations
   */
  private initializeModelTiers(): void {
    // Basic tier - fastest, cheapest
    this.modelTiers.set("basic", {
      tier: "basic",
      provider: "akash",
      model: "llama-3.1-8b-instruct",
      maxTokens: 1000,
      temperature: 0.1,
    });

    // Standard tier - balanced performance
    this.modelTiers.set("standard", {
      tier: "standard",
      provider: "gemini",
      model: "gemini-1.5-flash",
      maxTokens: 2000,
      temperature: 0.2,
    });

    // Premium tier - highest quality
    this.modelTiers.set("premium", {
      tier: "premium",
      provider: "gemini",
      model: "gemini-1.5-pro",
      maxTokens: 4000,
      temperature: 0.1,
    });

    logger.info(`Configured ${this.modelTiers.size} model tiers`);
  }

  /**
   * Get a specific provider by name
   */
  getProvider(name: string): AIProvider | null {
    return this.providers.get(name) || null;
  }

  /**
   * Get provider for a specific tier
   */
  getProviderForTier(
    tier: "basic" | "standard" | "premium"
  ): AIProvider | null {
    const config = this.modelTiers.get(tier);
    if (!config) {
      return null;
    }
    return this.getProvider(config.provider);
  }

  /**
   * Get model configuration for a specific tier
   */
  getModelConfigForTier(
    tier: "basic" | "standard" | "premium"
  ): ModelTierConfig | null {
    return this.modelTiers.get(tier) || null;
  }

  /**
   * Get all available providers
   */
  getAllProviders(): AIProvider[] {
    return Array.from(this.providers.values());
  }

  /**
   * Check health of all providers
   */
  async checkAllProvidersHealth(): Promise<Record<string, boolean>> {
    const healthChecks: Record<string, boolean> = {};

    const providers = Array.from(this.providers.entries());
    for (const [name, provider] of providers) {
      try {
        healthChecks[name] = await provider.isHealthy();
      } catch (error) {
        logger.warn(`Health check failed for provider ${name}:`, error);
        healthChecks[name] = false;
      }
    }

    return healthChecks;
  }

  /**
   * Get configuration for all providers
   */
  getAllProvidersConfig(): Record<string, Record<string, unknown>> {
    const configs: Record<string, Record<string, unknown>> = {};

    for (const [name, provider] of this.providers) {
      configs[name] = provider.getConfig();
    }

    return configs;
  }

  /**
   * Analyze text content using the factory
   */
  async analyzeTextContent(
    text: string,
    config: {
      hateSpeech?: boolean;
      harassment?: boolean;
      violence?: boolean;
      selfHarm?: boolean;
      sexual?: boolean;
      shocking?: boolean;
      illegal?: boolean;
      spam?: boolean;
    },
    tier: "basic" | "standard" | "premium" = "standard",
    oldMessages?: unknown[]
  ) {
    const provider = this.getProviderForTier(tier);
    const modelConfig = this.getModelConfigForTier(tier);

    if (!provider || !modelConfig) {
      throw new Error(`No provider configured for tier: ${tier}`);
    }

    try {
      return await provider.analyzeTextContent(
        text,
        config,
        oldMessages as unknown[],
        modelConfig.model
      );
    } catch (error) {
      logger.error(`Analysis failed with ${provider.name}:`, error);
      throw error;
    }
  }

  /**
   * Clear provider cache (if supported)
   */
  clearProviderCache(): void {
    logger.info("Clearing provider caches");
    // Implementation would depend on provider-specific cache clearing
  }

  /**
   * Validate configuration
   */
  validateConfiguration(): boolean {
    try {
      // Check if we have at least one provider
      if (this.providers.size === 0) {
        logger.error("No providers configured");
        return false;
      }

      // Check if all tiers have valid configurations
      for (const tier of ["basic", "standard", "premium"]) {
        const config = this.modelTiers.get(tier);
        if (!config) {
          logger.error(`No configuration for tier: ${tier}`);
          return false;
        }

        const provider = this.getProvider(config.provider);
        if (!provider) {
          logger.error(
            `Provider ${config.provider} not found for tier ${tier}`
          );
          return false;
        }
      }

      return true;
    } catch (error) {
      logger.error("Configuration validation failed:", error);
      return false;
    }
  }

  /**
   * Get provider statistics
   */
  getProviderStats(): Record<string, unknown> {
    const stats: Record<string, unknown> = {
      totalProviders: this.providers.size,
      configuredTiers: this.modelTiers.size,
      providers: {},
    };

    for (const [name, provider] of this.providers) {
      stats.providers = {
        ...(stats.providers as Record<string, unknown>),
        [name]: {
          name: provider.name,
          models: provider.getAvailableModels(),
          config: provider.getConfig(),
        },
      };
    }

    return stats;
  }
}

// Export singleton instance
export const AIServiceFactory = new AIServiceFactoryImpl();

// Export individual functions for convenience
export const getProvider = (name: string): AIProvider | null =>
  AIServiceFactory.getProvider(name);

export const getProviderForTier = (
  tier: "basic" | "standard" | "premium"
): AIProvider | null => AIServiceFactory.getProviderForTier(tier);

export const getModelConfigForTier = (
  tier: "basic" | "standard" | "premium"
): ModelTierConfig | null => AIServiceFactory.getModelConfigForTier(tier);

export const getAllProviders = (): AIProvider[] =>
  AIServiceFactory.getAllProviders();

export const checkAllProvidersHealth = (): Promise<Record<string, boolean>> =>
  AIServiceFactory.checkAllProvidersHealth();

export const getAllProvidersConfig = (): Record<
  string,
  Record<string, unknown>
> => AIServiceFactory.getAllProvidersConfig();

export const analyzeTextContentWithFactory = (
  text: string,
  config: {
    hateSpeech?: boolean;
    harassment?: boolean;
    violence?: boolean;
    selfHarm?: boolean;
    sexual?: boolean;
    shocking?: boolean;
    illegal?: boolean;
    spam?: boolean;
  },
  tier: "basic" | "standard" | "premium" = "standard",
  oldMessages?: unknown[]
) => AIServiceFactory.analyzeTextContent(text, config, tier, oldMessages);

export const clearProviderCache = (): void =>
  AIServiceFactory.clearProviderCache();

export const validateConfiguration = (): boolean =>
  AIServiceFactory.validateConfiguration();

export const getProviderStats = (): Record<string, unknown> =>
  AIServiceFactory.getProviderStats();
