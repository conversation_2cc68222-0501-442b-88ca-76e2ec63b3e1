/**
 * Akash Chat Provider wrapper
 * Wraps the existing akashChatService to implement the AIProvider interface
 */

import type {
  AIAnalysisResult,
  AIProvider,
  AIMessage,
} from "../interfaces/aiProvider";
import { config } from "../config";

/**
 * <PERSON>kash Chat AI Provider implementation
 * Wraps the existing akashChatService functionality
 */
export class AkashChatProvider implements AIProvider {
  readonly name = "akash";

  /**
   * Analyze text content through Akash Chat API
   * @param text Text to analyze
   * @param config Configuration for content filtering
   * @param oldMessages Previous messages for context
   * @param model Specific Akash model to use
   * @returns Analysis result with flags, reasoning, and filtered content
   */
  async analyzeTextContent(
    text: string,
    filterOptions: {
      hateSpeech?: boolean;
      harassment?: boolean;
      violence?: boolean;
      selfHarm?: boolean;
      sexual?: boolean;
      shocking?: boolean;
      illegal?: boolean;
      spam?: boolean;
    },
    oldMessages?: AIMessage[],
    model?: string
  ): Promise<AIAnalysisResult> {
    const modelName = model || config.akashChat.modelTiers.normal;
    console.log(`[Akash Provider] Analyzing text using model: ${modelName}`);

    // Import the akashChatService module to access its functions
    const akashService = await import("./akashChatService");

    try {
      // Convert config object to the format expected by akashChatService
      const filterConfig: Record<string, boolean> = {
        allowAbuse: !filterOptions.hateSpeech,
        allowHarassment: !filterOptions.harassment,
        allowViolence: !filterOptions.violence,
        allowSelfHarm: !filterOptions.selfHarm,
        allowSexual: !filterOptions.sexual,
        allowShocking: !filterOptions.shocking,
        allowIllegal: !filterOptions.illegal,
        allowSpam: !filterOptions.spam,
      };

      // Convert AIMessage[] to any[] for compatibility
      const legacyMessages = oldMessages || [];

      // Call the existing analyzeTextContent function with legacy parameters
      const result = await akashService.analyzeTextContent(
        text,
        legacyMessages,
        filterConfig,
        "normal"
      );

      console.log(
        `[Akash Provider] Analysis completed using model: ${modelName}`
      );

      // Convert the result to match AIAnalysisResult interface
      return {
        blocked: (result as any).isViolation || false,
        categories: (result as any).flags || [],
        confidence: 0.8, // Default confidence
        reasoning: (result as any).reason,
        model: modelName,
        provider: this.name,
        responseTime: 0, // Will be set by caller
        cached: false, // Will be set by caller
      };
    } catch (error) {
      console.error(`[Akash Provider] Error analyzing text:`, error);
      throw error;
    }
  }

  /**
   * Check if Akash Chat service is healthy
   * @returns Promise resolving to health status
   */
  async isHealthy(): Promise<boolean> {
    try {
      // Simple health check - try to analyze a minimal text
      const result = await this.analyzeTextContent(
        "health check",
        { hateSpeech: false, harassment: false, violence: false },
        [],
        config.akashChat.modelTiers.fast // Use fastest model for health check
      );

      return typeof result.blocked === "boolean";
    } catch (error) {
      console.error("[Akash Provider] Health check failed:", error);
      return false;
    }
  }

  /**
   * Get available Akash Chat models
   * @returns Array of model names
   */
  getAvailableModels(): string[] {
    return [
      config.akashChat.modelTiers.pro,
      config.akashChat.modelTiers.normal,
      config.akashChat.modelTiers.fast,
      // Add other known Akash models
      "Meta-Llama-3-3-70B-Instruct",
      "Meta-Llama-3-1-8B-Instruct-FP8",
      "Qwen3-235B-A22B-FP8",
    ];
  }

  /**
   * Get Akash Chat provider configuration
   * @returns Provider configuration
   */
  getConfig(): Record<string, any> {
    return {
      provider: this.name,
      baseUrl: config.akashChat.baseUrl,
      timeout: config.akashChat.timeout,
      hasApiKey: !!config.akashChat.apiKey,
      modelTiers: config.akashChat.modelTiers,
      availableModels: this.getAvailableModels(),
    };
  }
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use AkashChatProvider class instead
 */
export const analyzeTextContentWithAkash = async (
  text: string,
  oldMessages: any[] = [],
  filterConfig: Record<string, boolean> = {},
  modelName: string = "Meta-Llama-3-3-70B-Instruct"
): Promise<AIAnalysisResult> => {
  const provider = new AkashChatProvider();

  // Convert legacy parameters to new interface
  const filterOptions = {
    hateSpeech: !filterConfig.allowAbuse,
    harassment: !filterConfig.allowHarassment,
    violence: !filterConfig.allowViolence,
    selfHarm: !filterConfig.allowSelfHarm,
    sexual: !filterConfig.allowSexual,
    shocking: !filterConfig.allowShocking,
    illegal: !filterConfig.allowIllegal,
    spam: !filterConfig.allowSpam,
  };

  return provider.analyzeTextContent(
    text,
    filterOptions,
    oldMessages,
    modelName
  );
};
