/**
 * Akash Chat Provider wrapper
 * Wraps the existing akashChatService to implement the AIProvider interface
 */

import type { AIAnalysisResult, AIProvider } from "../interfaces/aiProvider";
import { config } from "../config";

/**
 * Akash Chat AI Provider implementation
 * Wraps the existing akashChatService functionality
 */
export class AkashChatProvider implements AIProvider {
  readonly name = "akash";

  /**
   * Analyze text content through Akash Chat API
   * @param text Text to analyze
   * @param oldMessages Previous messages for context
   * @param filterConfig Configuration for content filtering
   * @param modelName Specific Akash model to use
   * @returns Analysis result with flags, reasoning, and filtered content
   */
  async analyzeTextContent(
    text: string,
    oldMessages: any[] = [],
    filterConfig: Record<string, boolean> = {},
    modelName: string
  ): Promise<AIAnalysisResult> {
    console.log(`[Akash Provider] Analyzing text using model: ${modelName}`);

    // Import the akashChatService module to access its functions
    const akashService = await import("./akashChatService");

    // Store the original getModelForTier function
    const originalGetModelForTier = akashService.getModelForTier;

    try {
      // Temporarily override the getModelForTier function to return our specific model
      (akashService as any).getModelForTier = () => modelName;

      // Call the existing analyzeTextContent function
      const result = await akashService.analyzeTextContent(
        text,
        oldMessages,
        filterConfig,
        "normal"
      );

      console.log(
        `[Akash Provider] Analysis completed using model: ${modelName}`
      );

      return result;
    } finally {
      // Restore the original function
      (akashService as any).getModelForTier = originalGetModelForTier;
    }
  }

  /**
   * Check if Akash Chat service is healthy
   * @returns Promise resolving to health status
   */
  async isHealthy(): Promise<boolean> {
    try {
      // Simple health check - try to analyze a minimal text
      const result = await this.analyzeTextContent(
        "health check",
        [],
        { allowAbuse: true, allowEmail: true, allowPhone: true },
        config.akashChat.modelTiers.fast // Use fastest model for health check
      );

      return typeof result.isViolation === "boolean";
    } catch (error) {
      console.error("[Akash Provider] Health check failed:", error);
      return false;
    }
  }

  /**
   * Get available Akash Chat models
   * @returns Array of model names
   */
  getAvailableModels(): string[] {
    return [
      config.akashChat.modelTiers.pro,
      config.akashChat.modelTiers.normal,
      config.akashChat.modelTiers.fast,
      // Add other known Akash models
      "Meta-Llama-3-3-70B-Instruct",
      "Meta-Llama-3-1-8B-Instruct-FP8",
      "Qwen3-235B-A22B-FP8",
    ];
  }

  /**
   * Get Akash Chat provider configuration
   * @returns Provider configuration
   */
  getConfig(): Record<string, any> {
    return {
      provider: this.name,
      baseUrl: config.akashChat.baseUrl,
      timeout: config.akashChat.timeout,
      hasApiKey: !!config.akashChat.apiKey,
      modelTiers: config.akashChat.modelTiers,
      availableModels: this.getAvailableModels(),
    };
  }
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use AkashChatProvider class instead
 */
export const analyzeTextContentWithAkash = async (
  text: string,
  oldMessages: any[] = [],
  filterConfig: Record<string, boolean> = {},
  modelName: string = "Meta-Llama-3-3-70B-Instruct"
): Promise<AIAnalysisResult> => {
  const provider = new AkashChatProvider();
  return provider.analyzeTextContent(
    text,
    oldMessages,
    filterConfig,
    modelName
  );
};
