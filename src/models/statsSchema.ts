import { sql } from "drizzle-orm";
import { date, integer, pgTable, text, timestamp } from "drizzle-orm/pg-core";

/**
 * Daily request statistics table
 */
export const requestStatsDaily = pgTable("request_stats_daily", {
  id: text("id").primaryKey(),
  date: date("date").notNull(),
  totalRequests: integer("total_requests").notNull().default(0),
  blockedRequests: integer("blocked_requests").notNull().default(0),
  filteredRequests: integer("filtered_requests").notNull().default(0),
  averageResponseTime: integer("average_response_time").notNull().default(0),
  cacheHitRate: integer("cache_hit_rate").notNull().default(0),
  createdAt: timestamp("created_at")
    .notNull()
    .default(sql`now()`),
  updatedAt: timestamp("updated_at")
    .notNull()
    .default(sql`now()`),
});

/**
 * Hourly API performance statistics table
 */
export const apiPerformanceHourly = pgTable("api_performance_hourly", {
  id: text("id").primaryKey(),
  hour: timestamp("hour").notNull(),
  timestamp: timestamp("timestamp").notNull(),
  endpoint: text("endpoint").notNull(),
  apiType: text("api_type").notNull(),
  requestCount: integer("request_count").notNull().default(0),
  averageResponseTime: integer("average_response_time").notNull().default(0),
  errorCount: integer("error_count").notNull().default(0),
  createdAt: timestamp("created_at")
    .notNull()
    .default(sql`now()`),
  updatedAt: timestamp("updated_at")
    .notNull()
    .default(sql`now()`),
});

/**
 * Daily content flags statistics table
 */
export const contentFlagsDaily = pgTable("content_flags_daily", {
  id: text("id").primaryKey(),
  date: date("date").notNull(),
  category: text("category").notNull(),
  flagName: text("flag_name").notNull(),
  flagCount: integer("flag_count").notNull().default(0),
  createdAt: timestamp("created_at")
    .notNull()
    .default(sql`now()`),
  updatedAt: timestamp("updated_at")
    .notNull()
    .default(sql`now()`),
});

/**
 * Daily user activity statistics table
 */
export const userActivityDaily = pgTable("user_activity_daily", {
  id: text("id").primaryKey(),
  date: date("date").notNull(),
  userId: text("user_id").notNull(),
  requestCount: integer("request_count").notNull().default(0),
  blockedCount: integer("blocked_count").notNull().default(0),
  createdAt: timestamp("created_at")
    .notNull()
    .default(sql`now()`),
  updatedAt: timestamp("updated_at")
    .notNull()
    .default(sql`now()`),
});

// Type definitions for the tables
export type RequestStatsDaily = typeof requestStatsDaily.$inferSelect;
export type NewRequestStatsDaily = typeof requestStatsDaily.$inferInsert;

export type ApiPerformanceHourly = typeof apiPerformanceHourly.$inferSelect;
export type NewApiPerformanceHourly = typeof apiPerformanceHourly.$inferInsert;

export type ContentFlagsDaily = typeof contentFlagsDaily.$inferSelect;
export type NewContentFlagsDaily = typeof contentFlagsDaily.$inferInsert;

export type UserActivityDaily = typeof userActivityDaily.$inferSelect;
export type NewUserActivityDaily = typeof userActivityDaily.$inferInsert;
