/**
 * AI Provider Interfaces
 * Defines the contract for AI content analysis providers
 */

export interface AIMessage {
  role: "user" | "assistant" | "system";
  content: string;
}

export interface PreScreeningResult {
  needsAIReview: boolean;
  blockedByPreScreening: boolean;
  flaggedCategories: string[];
  confidence: number;
}

export interface AIAnalysisResult {
  blocked: boolean;
  categories: string[];
  confidence: number;
  reasoning?: string;
  model?: string;
  provider?: string;
  responseTime?: number;
  cached?: boolean;
  preScreening?: PreScreeningResult;
  messages?: unknown[];
}

export interface ModelTierConfig {
  tier: "basic" | "standard" | "premium";
  provider: string;
  model: string;
  maxTokens?: number;
  temperature?: number;
  systemPrompt?: string;
}

export interface AIProvider {
  name: string;
  isHealthy(): Promise<boolean>;
  getAvailableModels(): string[];
  getConfig(): Record<string, unknown>;
  analyzeTextContent(
    text: string,
    config: {
      hateSpeech?: boolean;
      harassment?: boolean;
      violence?: boolean;
      selfHarm?: boolean;
      sexual?: boolean;
      shocking?: boolean;
      illegal?: boolean;
      spam?: boolean;
    },
    oldMessages?: AIMessage[],
    model?: string
  ): Promise<AIAnalysisResult>;
}

export interface AIProviderFactory {
  getProvider(name: string): AIProvider | null;
  getProviderForTier(tier: "basic" | "standard" | "premium"): AIProvider | null;
  getModelConfigForTier(tier: "basic" | "standard" | "premium"): ModelTierConfig | null;
  getAllProviders(): AIProvider[];
  checkAllProvidersHealth(): Promise<Record<string, boolean>>;
  getAllProvidersConfig(): Record<string, Record<string, unknown>>;
}
