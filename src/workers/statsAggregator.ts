#!/usr/bin/env bun
import {
  aggregateAndStoreApiPerformance,
  aggregateAndStoreContentFlags,
  aggregateAndStoreRequestStats,
  aggregateAndStoreUserActivity,
  waitForRedisReady,
} from "../services/statsDbService";
import { isRedisHealthy } from "../utils/redis";
import logger from "../utils/logger";

// Global flag to prevent multiple aggregations
let isAggregating = false;

/**
 * Get keys matching a pattern (fallback implementation)
 */
const getKeysMatchingPattern = async (pattern: string): Promise<string[]> => {
  try {
    // This is a simplified implementation
    // In a real Redis setup, you'd use SCAN command
    const keys: string[] = [];
    
    // For now, return empty array as we don't have a proper SCAN implementation
    // This prevents errors but doesn't actually clear keys
    logger.warn(`Pattern matching not implemented for: ${pattern}`);
    
    return keys;
  } catch (error) {
    logger.error(`Error getting keys for pattern ${pattern}:`, error);
    return [];
  }
};

/**
 * Delete a batch of keys
 */
const deleteBatch = async (keys: string[]): Promise<void> => {
  try {
    // Delete keys one by one to avoid issues
    for (const key of keys) {
      try {
        // Use the cache delete function which handles fallbacks
        const { cacheDelete } = await import("../utils/redis");
        await cacheDelete(key);
      } catch (error) {
        logger.error(`Failed to delete key ${key}:`, error);
      }
    }
  } catch (error) {
    logger.error("Error deleting batch of keys:", error);
  }
};

/**
 * Clear processed Redis keys after aggregation
 */
const clearProcessedRedisKeys = async (date: string): Promise<void> => {
  try {
    logger.info(`Clearing processed Redis keys for date: ${date}`);

    // Define key patterns to clear
    const keyPatterns = [
      `stats:requests:${date}:*`,
      `stats:api_performance:${date}:*`,
      `stats:content_flags:${date}:*`,
      `stats:user_activity:${date}:*`,
    ];

    // Clear keys for each pattern
    for (const pattern of keyPatterns) {
      try {
        // Get all keys matching the pattern
        const keys = await getKeysMatchingPattern(pattern);
        
        if (keys.length > 0) {
          // Delete keys in batches to avoid blocking Redis
          const batchSize = 100;
          for (let i = 0; i < keys.length; i += batchSize) {
            const batch = keys.slice(i, i + batchSize);
            await deleteBatch(batch);
          }
          logger.info(`Cleared ${keys.length} keys for pattern: ${pattern}`);
        }
      } catch (error) {
        logger.error(`Failed to clear keys for pattern ${pattern}:`, error);
      }
    }

    logger.info("Finished clearing processed Redis keys");
  } catch (error) {
    logger.error("Error clearing processed Redis keys:", error);
  }
};

/**
 * Main stats aggregation function
 * Aggregates stats from Redis and stores them in the database
 */
export const runStatsAggregation = async (): Promise<void> => {
  // Prevent concurrent aggregations
  if (isAggregating) {
    logger.info("Stats aggregation already in progress, skipping");
    return;
  }

  try {
    // Set the flag atomically
    isAggregating = true;
    
    logger.info("Starting stats aggregation");

    // Wait for Redis to be ready
    await waitForRedisReady();

    // Check if Redis is healthy before proceeding
    const redisHealthy = await isRedisHealthy();
    if (!redisHealthy) {
      logger.warn("Redis is not healthy, skipping stats aggregation");
      return;
    }

    // Get current date for aggregation
    const currentDate = new Date().toISOString().split("T")[0];

    // Run all aggregations in parallel for better performance
    const aggregationPromises = [
      aggregateAndStoreRequestStats(currentDate),
      aggregateAndStoreApiPerformance(currentDate),
      aggregateAndStoreContentFlags(currentDate),
      aggregateAndStoreUserActivity(currentDate),
    ];

    // Wait for all aggregations to complete
    const results = await Promise.allSettled(aggregationPromises);

    // Log results
    results.forEach((result, index) => {
      const aggregationType = [
        "Request Stats",
        "API Performance",
        "Content Flags",
        "User Activity",
      ][index];

      if (result.status === "fulfilled") {
        logger.info(`${aggregationType} aggregation completed successfully`);
      } else {
        logger.error(`${aggregationType} aggregation failed:`, result.reason);
      }
    });

    // Clear processed Redis keys after successful aggregation
    await clearProcessedRedisKeys(currentDate);

    logger.info("Stats aggregation completed");
  } catch (error) {
    logger.error("Stats aggregation failed:", error);
    throw error;
  } finally {
    // Always reset the flag
    isAggregating = false;
  }
};

/**
 * Reset Redis counter for a specific key
 */
export const resetRedisCounter = async (key: string): Promise<void> => {
  try {
    const { cacheSet } = await import("../utils/redis");
    await cacheSet(key, "0");
    logger.debug(`Reset Redis counter: ${key}`);
  } catch (error) {
    logger.error(`Failed to reset Redis counter ${key}:`, error);
  }
};

/**
 * Reset Redis hash for a specific key
 */
export const resetRedisHash = async (key: string): Promise<void> => {
  try {
    const { cacheDelete } = await import("../utils/redis");
    await cacheDelete(key);
    logger.debug(`Reset Redis hash: ${key}`);
  } catch (error) {
    logger.error(`Failed to reset Redis hash ${key}:`, error);
  }
};

/**
 * Graceful shutdown handler
 */
const gracefulShutdown = (signal: string): void => {
  logger.info(`Received ${signal}, shutting down gracefully`);
  
  if (isAggregating) {
    logger.info("Waiting for current aggregation to complete...");
    // In a real implementation, you might want to wait for the current operation
    // For now, we'll just log and exit
  }
  
  logger.info("Stats aggregator shutdown complete");
  throw new Error(`Received ${signal}, shutting down`);
};

/**
 * Error handler for unhandled errors
 */
const errorHandler = (error: Error): void => {
  logger.error("Unhandled error in stats aggregator:", error);
  throw new Error("Unhandled error in stats aggregator");
};

// Set up signal handlers for graceful shutdown
process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
process.on("SIGINT", () => gracefulShutdown("SIGINT"));

// Set up error handlers
process.on("uncaughtException", errorHandler);
process.on("unhandledRejection", (reason) => {
  logger.error("Unhandled promise rejection:", reason);
  throw new Error("Unhandled promise rejection");
});

// Export for testing
export { isAggregating };
