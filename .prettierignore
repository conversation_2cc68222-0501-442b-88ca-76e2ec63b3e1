# Build output
dist/
build/
out/

# Dependencies
node_modules/

# Package manager lock files
package-lock.json
yarn.lock
bun.lockb

# Environment files
.env
.env.*

# Logs
*.log
logs/

# Coverage
coverage/
*.lcov
.nyc_output

# TypeScript cache
*.tsbuildinfo

# Cache directories
.cache
.parcel-cache
.eslintcache
.prettiercache

# Generated files
*.d.ts

# Database files
*.sqlite
*.sqlite3
*.db
dump.rdb

# OS generated files
.DS_Store
Thumbs.db

# Editor files
.vscode/
.idea/
*.swp
*.swo

# Reports
eslint-report.json
eslint-report.html

# Docker files
Dockerfile*
docker-compose*.yml

# Temporary files
tmp/
temp/

# Markdown files (optional - remove if you want to format them)
# *.md
# README.md

# Configuration files that should not be formatted
.github/
.husky/
